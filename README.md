
# School Website – Team 4

This project is the official website for **<PERSON><PERSON><PERSON>**, built using **React** and **Vite**. It includes a well-organized layout, routing, and reusable components to ensure a smooth and modern user experience.

##  Tech Stack

- **React** – Frontend library
- **Vite** – Build tool for fast development
- **Tailwind CSS** – Utility-first CSS framework
- **React Router** – Page navigation
- **ESLint** – Linting and code quality

##  Project Setup

```bash
npm install
npm run dev





