import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import tailwindcss from "@tailwindcss/vite";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), tailwindcss()],
  build: {
    rollupOptions: {
      input: {
        main: "./index.html",
        server: "./src/entry-server.jsx",
      },
    },
  },
  ssr: {
    noExternal: ["react-router-dom"],
  },
});
