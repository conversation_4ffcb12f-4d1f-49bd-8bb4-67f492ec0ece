import { Link } from "react-router-dom";
import { useState } from "react";
import { motion, AnimatePresence } from "framer-motion";

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const menuVariants = {
    closed: {
      opacity: 0,
      height: 0,
      transition: {
        duration: 0.3,
        ease: "easeInOut",
      },
    },
    open: {
      opacity: 1,
      height: "auto",
      transition: {
        duration: 0.3,
        ease: "easeInOut",
      },
    },
  };

  const linkVariants = {
    closed: { opacity: 0, y: -10 },
    open: { opacity: 1, y: 0 },
  };

  return (
    <header
      className="sticky top-0 z-50 text-white shadow-lg"
      style={{ background: "linear-gradient(90deg, #2563eb 0%, #38bdf8 100%)" }}
    >
      <nav className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo and Brand */}
          <div className="flex items-center gap-2 sm:gap-3">
            <Link to="/" className="flex items-center gap-2 sm:gap-3">
              <img
                src="src/assets/vbss-logo.png"
                alt="Logo"
                className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-10 object-contain"
              />
              <h1 className="text-sm sm:text-lg lg:text-2xl font-bold truncate">
                <span className="hidden sm:inline">
                  Vishwa Bharati Shiksha Sadan
                </span>
                <span className="sm:hidden">VBSS</span>
              </h1>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <ul className="hidden md:flex gap-1 lg:gap-4">
            <li>
              <Link
                to="/"
                className="px-3 lg:px-4 py-2 rounded-md hover:bg-blue-700 hover:scale-105 transition-all duration-300 font-semibold text-sm lg:text-base"
              >
                Home
              </Link>
            </li>
            <li>
              <Link
                to="/about"
                className="px-3 lg:px-4 py-2 rounded-md hover:bg-blue-700 hover:scale-105 transition-all duration-300 font-semibold text-sm lg:text-base"
              >
                About
              </Link>
            </li>
            <li>
              <Link
                to="/student"
                className="px-3 lg:px-4 py-2 rounded-md hover:bg-blue-700 hover:scale-105 transition-all duration-300 font-semibold text-sm lg:text-base"
              >
                Student
              </Link>
            </li>
            <li>
              <Link
                to="/registration"
                className="px-3 lg:px-4 py-2 rounded-md hover:bg-blue-700 hover:scale-105 transition-all duration-300 font-semibold text-sm lg:text-base"
              >
                Register
              </Link>
            </li>
            <li>
              <Link
                to="/contact"
                className="px-3 lg:px-4 py-2 rounded-md hover:bg-blue-700 hover:scale-105 transition-all duration-300 font-semibold text-sm lg:text-base"
              >
                Contact
              </Link>
            </li>
          </ul>

          {/* Mobile Menu Button */}
          <button
            onClick={toggleMenu}
            className="md:hidden p-2 rounded-md hover:bg-blue-700 transition-colors duration-300 focus:outline-none focus:ring-2 focus:ring-white"
            aria-label="Toggle menu"
          >
            <motion.div
              animate={isMenuOpen ? "open" : "closed"}
              className="w-6 h-6 flex flex-col justify-center items-center"
            >
              <motion.span
                variants={{
                  closed: { rotate: 0, y: 0 },
                  open: { rotate: 45, y: 6 },
                }}
                className="w-6 h-0.5 bg-white block transition-all duration-300 origin-center"
              />
              <motion.span
                variants={{
                  closed: { opacity: 1 },
                  open: { opacity: 0 },
                }}
                className="w-6 h-0.5 bg-white block mt-1.5 transition-all duration-300"
              />
              <motion.span
                variants={{
                  closed: { rotate: 0, y: 0 },
                  open: { rotate: -45, y: -6 },
                }}
                className="w-6 h-0.5 bg-white block mt-1.5 transition-all duration-300 origin-center"
              />
            </motion.div>
          </button>
        </div>

        {/* Mobile Navigation */}
        <AnimatePresence>
          {isMenuOpen && (
            <motion.div
              variants={menuVariants}
              initial="closed"
              animate="open"
              exit="closed"
              className="md:hidden overflow-hidden"
            >
              <div className="px-2 pt-2 pb-3 space-y-1 bg-blue-800 bg-opacity-95 rounded-lg mt-2 mb-4">
                {[
                  { to: "/", label: "Home" },
                  { to: "/about", label: "About" },
                  { to: "/student", label: "Student" },
                  { to: "/registration", label: "Register" },
                  { to: "/contact", label: "Contact" },
                ].map((link, index) => (
                  <motion.div
                    key={link.to}
                    variants={linkVariants}
                    initial="closed"
                    animate="open"
                    transition={{ delay: index * 0.1 }}
                  >
                    <Link
                      to={link.to}
                      onClick={() => setIsMenuOpen(false)}
                      className="block px-3 py-2 rounded-md text-base font-medium hover:bg-blue-700 hover:text-white transition-colors duration-300"
                    >
                      {link.label}
                    </Link>
                  </motion.div>
                ))}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </nav>
    </header>
  );
}
