import { createBrowserRouter } from "react-router-dom";
import Layout from "./components/Layout";
import Home from "./pages/Home";
import About from "./pages/About";
import Gallery from "./pages/Gallery";
import Facilities from "./pages/Facilities";
import Student from "./pages/Student";
import Registration from "./pages/Registration";
import Contact from "./pages/Contact";
import Teacherdata from "./pages/Teacherdata";

// Data Mode Router Configuration for React Router v7.7.0
export const router = createBrowserRouter([
  {
    path: "/",
    element: <Layout />,
    children: [
      {
        index: true,
        element: <Home />,
        loader: async () => {
          return { title: "Home - SMP Negeri 1 Cibadak" };
        },
      },
      {
        path: "about",
        element: <About />,
        loader: async () => {
          return { title: "About - SMP Negeri 1 Cibadak" };
        },
      },
      {
        path: "gallery",
        element: <Gallery />,
        loader: async () => {
          return { title: "Gallery - SMP Negeri 1 Cibadak" };
        },
      },
      {
        path: "facilities",
        element: <Facilities />,
        loader: async () => {
          return { title: "Facilities - SMP Negeri 1 Cibadak" };
        },
      },
      {
        path: "student",
        element: <Student />,
        loader: async () => {
          return { title: "Student - SMP Negeri 1 Cibadak" };
        },
      },
      {
        path: "registration",
        element: <Registration />,
        loader: async () => {
          return { title: "Registration - SMP Negeri 1 Cibadak" };
        },
      },
      {
        path: "contact",
        element: <Contact />,
        loader: async () => {
          return { title: "Contact - SMP Negeri 1 Cibadak" };
        },
      },
      {
        path: "teacher-data",
        element: <Teacherdata />,
        loader: async () => {
          return { title: "Teacher Data - SMP Negeri 1 Cibadak" };
        },
      },
    ],
    errorElement: (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-gray-800 mb-4">404</h1>
          <p className="text-gray-600">Page not found</p>
        </div>
      </div>
    ),
  },
]);
