import { StrictMode } from "react";
import { renderToString } from "react-dom/server";
import {
  createStaticRouter,
  StaticRouterProvider,
} from "react-router-dom/server";
import { router } from "./router.jsx";

export function render(_url, context = {}) {
  const staticRouter = createStaticRouter(router.routes, context);

  return renderToString(
    <StrictMode>
      <StaticRouterProvider
        router={staticRouter}
        context={context}
        nonce={undefined}
      />
    </StrictMode>
  );
}
