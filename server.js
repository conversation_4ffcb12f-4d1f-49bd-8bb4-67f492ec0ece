import express from "express";
import { readFileSync } from "fs";
import { fileURLToPath } from "url";
import { dirname, resolve } from "path";

const __dirname = dirname(fileURLToPath(import.meta.url));
const app = express();

// Serve static files
app.use(express.static(resolve(__dirname, "dist/client")));

// SSR handler
app.get("*", async (req, res) => {
  try {
    // Read the HTML template
    let template = readFileSync(resolve(__dirname, "dist/client/index.html"), "utf-8");
    
    // Import the server entry point
    const { render } = await import("./dist/server/entry-server.js");
    
    // Render the app
    const appHtml = render(req.url, {});
    
    // Replace the placeholder with the rendered app
    const html = template.replace("<!--ssr-outlet-->", appHtml);
    
    res.status(200).set({ "Content-Type": "text/html" }).end(html);
  } catch (error) {
    console.error("SSR Error:", error);
    res.status(500).end("Internal Server Error");
  }
});

const port = process.env.PORT || 3000;
app.listen(port, () => {
  console.log(`SSR Server running on http://localhost:${port}`);
});
