{"name": "school-website-team4", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "npm run build:client && npm run build:server", "build:client": "vite build --outDir dist/client", "build:server": "vite build --ssr src/entry-server.jsx --outDir dist/server", "serve": "node server.js", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@react-router/node": "^7.7.0", "@react-router/serve": "^7.7.0", "@tailwindcss/vite": "^4.1.11", "express": "^5.1.0", "framer-motion": "^12.23.6", "lucide-react": "^0.525.0", "react": "^19.1.0", "react-dom": "^19.1.0", "react-icons": "^5.5.0", "react-router-dom": "^7.7.0"}, "devDependencies": {"@eslint/js": "^9.30.1", "@vitejs/plugin-react": "^4.6.0", "autoprefixer": "^10.4.21", "eslint": "^9.30.1", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.3.0", "postcss": "^8.5.6", "tailwindcss": "^4.1.11", "vite": "^7.0.4"}}